package com.morningstar.martapi.validator.portfolioholdings;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.morningstar.martapi.exception.HoldingValidationException;
import com.morningstar.martgateway.domains.fixedincome.entity.FixedIncomeEnrichmentType;
import com.morningstar.martgateway.domains.portfolioholding.entity.HoldingsView;
import com.morningstar.martgateway.domains.portfolioholding.entity.PortfolioDateType;
import com.morningstar.martgateway.domains.portfolioholding.entity.PortfolioDepth;
import com.morningstar.martgateway.domains.portfolioholding.entity.SuppressionTypeEnum;
import com.morningstar.martgateway.interfaces.model.holdingdata.HoldingDataRequest;
import com.morningstar.martgateway.interfaces.model.holdingdata.PortfolioDate;
import com.morningstar.martgateway.interfaces.model.holdingdata.SuppressionType;
import java.util.HashSet;
import java.util.Set;
import org.junit.Test;

public class PortfolioSettingsValidatorTest {

    private final PortfolioSettingsValidator portfolioSettingsValidator = new PortfolioSettingsValidator();

    @Test
    public void validate() {
        HoldingDataRequest request = new HoldingDataRequest();

        // PortfolioSetting
        HoldingValidationException exception = assertExceptionThrown(request);
        assertEquals("Mandatory field `portfolioSetting` is missing", exception.getMessage());

        // PortfolioDepth
        HoldingDataRequestBuilder builder = new HoldingDataRequestBuilder();
        request = builder.build();
        exception = assertExceptionThrown(request);
        assertEquals( "Mandatory field `portfolioDepth` is missing", exception.getMessage());
        builder.portfolioDepth(PortfolioDepth.BASE_LEVEL);

        // HoldingsView
        request = builder.build();
        exception = assertExceptionThrown(request);
        assertEquals( "Mandatory field `holdingsView` is missing", exception.getMessage());

        builder.holdingsView(new HoldingsView("badValue"));
        request = builder.build();
        exception = assertExceptionThrown(request);
        assertEquals( "Invalid holdingsView value. Valid values: [bestAvailable, full] or an integer >= 1", exception.getMessage());
        builder.holdingsView(new HoldingsView("full"));

        // PortfolioDate
        request = builder.portfolioDate(null).build();
        exception = assertExceptionThrown(request);
        assertEquals( "Mandatory field `portfolioDate` is missing", exception.getMessage());

        request = builder.portfolioDate(new PortfolioDate()).build();
        exception = assertExceptionThrown(request);
        assertEquals( "Mandatory field `portfolioDate.type` is missing", exception.getMessage());

        // MostRecent
        builder.portfolioDateType(PortfolioDateType.MOST_RECENT);
        exception = assertExceptionThrown(request);
        assertEquals( "Mandatory field `frequency` is missing", exception.getMessage());

        request = builder.frequency("z").build();
        exception = assertExceptionThrown(request);
        assertEquals( "Invalid frequency value. Valid values: [d, m]", exception.getMessage());

        request = builder.frequency("d").startDate("2021-01-01").build();
        exception = assertExceptionThrown(request);
        assertEquals( "Prohibited field `startDate` is provided for type `mostRecent`", exception.getMessage());
        builder.startDate(null).build();

        request = builder.endDate("2022-01-01").build();
        exception = assertExceptionThrown(request);
        assertEquals( "Prohibited field `endDate` is provided for type `mostRecent`", exception.getMessage());
        builder.endDate(null).build();

        request = builder.staticDates(Set.of("2023-01-01")).build();
        exception = assertExceptionThrown(request);
        assertEquals( "Prohibited field `dates` is provided for type `mostRecent`", exception.getMessage());
        builder.staticDates(null).build();

        // TimeSeries
        builder.portfolioDateType(PortfolioDateType.TIME_SERIES);
        request = builder.frequency(null).build();
        exception = assertExceptionThrown(request);
        assertEquals( "Mandatory field `frequency` is missing", exception.getMessage());

        builder.frequency("z");
        exception = assertExceptionThrown(request);
        assertEquals( "Invalid frequency value. Valid values: [d, m]", exception.getMessage());

        request = builder.frequency("d").build();
        exception = assertExceptionThrown(request);
        assertEquals( "Mandatory field `startDate` is missing", exception.getMessage());

        request = builder.startDate("badDate").build();
        exception = assertExceptionThrown(request);
        assertEquals( "Invalid `startDate`. Valid format is [yyyy-MM-dd]", exception.getMessage());

        request = builder.startDate("2022-01-01").build();
        exception = assertExceptionThrown(request);
        assertEquals( "Mandatory field `endDate` is missing", exception.getMessage());

        request = builder.endDate("badDate").build();
        exception = assertExceptionThrown(request);
        assertEquals( "Invalid `endDate`. Valid format is [yyyy-MM-dd]", exception.getMessage());

        request = builder.endDate("1900-01-01").build();
        exception = assertExceptionThrown(request);
        assertEquals( "startDate cannot be after endDate", exception.getMessage());

        request = builder.endDate("2031-01-01").build();
        exception = assertExceptionThrown(request);
        assertEquals( "endDate cannot be after today", exception.getMessage());

        request = builder.endDate("2024-06-31").build();
        exception = assertExceptionThrown(request);
        assertEquals( "Invalid `endDate`. Valid format is [yyyy-MM-dd]", exception.getMessage());
        builder.endDate("2024-01-01").build();

        request = builder.staticDates(Set.of("2024-01-01")).build();
        exception = assertExceptionThrown(request);
        assertEquals( "Prohibited field `dates` is provided for type `timeSeries`", exception.getMessage());
        builder.staticDates(null).build();

        // StaticDates
        request = builder.portfolioDateType(PortfolioDateType.STATIC_DATES).build();
        exception = assertExceptionThrown(request);
        assertEquals( "Prohibited field `startDate` is provided for type `staticDates`", exception.getMessage());

        request = builder.startDate(null).build();
        exception = assertExceptionThrown(request);
        assertEquals( "Prohibited field `endDate` is provided for type `staticDates`", exception.getMessage());

        request = builder.endDate(null).build();
        exception = assertExceptionThrown(request);
        assertEquals( "Mandatory field `dates` is missing or empty for type `staticDates`", exception.getMessage());

        request = builder.staticDates(Set.of()).build();
        exception = assertExceptionThrown(request);
        assertEquals( "Mandatory field `dates` is missing or empty for type `staticDates`", exception.getMessage());

        request = builder.staticDates(Set.of("2023-01-01","2024-06-31")).build();
        exception = assertExceptionThrown(request);
        assertEquals( "Invalid date format in `dates`. Valid format is [yyyy-MM-dd]", exception.getMessage());

        request = builder.staticDates(Set.of("2023-01-01","badDate")).build();
        exception = assertExceptionThrown(request);
        assertEquals( "Invalid date format in `dates`. Valid format is [yyyy-MM-dd]", exception.getMessage());

        // Suppression
        request = builder.staticDates(createNDates(15)).suppressionType(null).build();
        exception = assertExceptionThrown(request);
        assertEquals( "Mandatory field `suppressionType` is missing", exception.getMessage());

        request = builder.suppressionType(new SuppressionType()).build();
        exception = assertExceptionThrown(request);
        assertEquals( "Mandatory field `suppressionType.type` is missing", exception.getMessage());

        request = builder.suppressionTypeEnum(SuppressionTypeEnum.CLIENT_SPECIFIC).build();
        exception = assertExceptionThrown(request);
        assertEquals( "Mandatory field `clientId` is missing for type `clientSpecific`", exception.getMessage());

        builder.suppressionClientId("someClientId");
        exception = assertExceptionThrown(request);
        assertEquals( "Suppression client id does not match id from token", exception.getMessage());

        request = builder.suppressionTypeEnum(SuppressionTypeEnum.DEFAULT).build();
        exception = assertExceptionThrown(request);
        assertEquals( "Prohibited field `clientId` is present for type `default`", exception.getMessage());

        builder.suppressionClientId(null);
        request = builder.suppressionTypeEnum(SuppressionTypeEnum.DEFAULT).build();

        portfolioSettingsValidator.validate(request);
    }

    @Test
    public void testPortfolioDateTypes() {
        HoldingDataRequestBuilder builder = new HoldingDataRequestBuilder();
        builder.suppressionType(new SuppressionType(SuppressionTypeEnum.DEFAULT, null));
        builder.holdingsView(new HoldingsView("full")).portfolioDepth(PortfolioDepth.BASE_LEVEL);
        builder.fixedIncomeEnrichmentType(FixedIncomeEnrichmentType.POINT_IN_TIME);

        // Test MOST_RECENT
        PortfolioDate mostRecentDate = new PortfolioDate();
        mostRecentDate.setType(PortfolioDateType.MOST_RECENT);
        mostRecentDate.setFrequency("d");
        builder.portfolioDate(mostRecentDate);
        assertDoesNotThrow(() -> portfolioSettingsValidator.validate(builder.build()));

        // Test TIME_SERIES
        PortfolioDate timeSeriesDate = new PortfolioDate();
        timeSeriesDate.setType(PortfolioDateType.TIME_SERIES);
        timeSeriesDate.setFrequency("d");
        timeSeriesDate.setStartDate("2023-01-01");
        timeSeriesDate.setEndDate("2023-12-31");
        builder.portfolioDate(timeSeriesDate);
        assertDoesNotThrow(() -> portfolioSettingsValidator.validate(builder.build()));

        // Test STATIC_DATES
        PortfolioDate staticDatesDate = new PortfolioDate();
        staticDatesDate.setType(PortfolioDateType.STATIC_DATES);
        staticDatesDate.setStaticDates(Set.of("2023-01-01"));
        builder.portfolioDate(staticDatesDate);
        assertDoesNotThrow(() -> portfolioSettingsValidator.validate(builder.build()));
    }

    private HoldingValidationException assertExceptionThrown(HoldingDataRequest request) {
        HoldingValidationException holdingValidationException = assertThrows(
                HoldingValidationException.class, () -> portfolioSettingsValidator.validate(request));
        assertEquals("400", holdingValidationException.getStatus().getCode());
        return holdingValidationException;
    }


    private Set<String> createNDates(int n) {
        if (n < 1 || n > 30) {
            throw new IllegalArgumentException("n must be between 1 and 30");
        }
        Set<String> dates = new HashSet<>();
        for (int i = 1; i < n; i++) {
            String day;
            if (i < 10) {
                day = "0" + i;
            } else {
                day = String.valueOf(i);
            }
            dates.add("2022-01-" + day);
        }
        return dates;
    }
}