package com.morningstar.martapi.validator;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.service.JwtVerificationService;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.UUID;

import static org.mockito.Mockito.*;

public class AuthTokenValidatorTest {

    private AuthTokenValidator validator;
    private JwtVerificationService jwtVerificationService;

    @Before
    public void setup() {
        jwtVerificationService = mock(JwtVerificationService.class);
        this.validator = new AuthTokenValidator(jwtVerificationService);
    }

    @Test
    public void validate() {
        HeadersAndParams headersAndParams1 = HeadersAndParams.builder()
                .requestId(UUID.randomUUID().toString())
                .productId("MDS")
                .authorizationToken("Bearer azxs")
                .build();
        InvestmentApiValidationException exception = Assertions.assertThrows(InvestmentApiValidationException.class,
                () -> validator.validate(headersAndParams1));
        Assertions.assertEquals(Status.INVALID_TOKEN.getCode(),exception.getStatus().getCode());

        HeadersAndParams headersAndParams2 = HeadersAndParams.builder()
                .requestId(UUID.randomUUID().toString())
                .productId("MDS")
                .authorizationToken(createExpiredToken())
                .build();
        exception = Assertions.assertThrows(InvestmentApiValidationException.class,
                () -> validator.validate(headersAndParams2));
        Assertions.assertEquals(Status.EXPIRED_TOKEN.getCode(),exception.getStatus().getCode());

        HeadersAndParams headersAndParams3 = HeadersAndParams.builder()
                .requestId(UUID.randomUUID().toString())
                .productId("MDS")
                .authorizationToken(createToken(""))
                .build();
        exception = Assertions.assertThrows(InvestmentApiValidationException.class,
                () -> validator.validate(headersAndParams3));
        Assertions.assertEquals(Status.INVALID_TOKEN.getCode(),exception.getStatus().getCode());
    }

    private String createToken(String userId) {
        return createToken(ZonedDateTime.now(ZoneOffset.UTC).plusDays(2).toInstant(), userId);
    }

    private String createExpiredToken() {
        return createToken(ZonedDateTime.now(ZoneOffset.UTC).minusDays(10).toInstant(), UUID.randomUUID().toString());
    }

    private String createToken(Instant expiration, String userId) {
        return JWT.create()
                .withExpiresAt(expiration)
                .withClaim("https://morningstar.com/mstar_id", userId)
                .sign(Algorithm.none());
    }
}
