package com.morningstar.martapi.validator.portfolioholdings;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.morningstar.martcommon.entity.result.request.IdMapper;
import com.morningstar.martgateway.domains.core.entity.entitlement.CachedEntitlement;
import com.morningstar.martgateway.domains.entitlement.entity.InvestmentApiIdType;
import com.morningstar.martgateway.domains.fixedincome.entity.FixedIncomeEnrichmentType;
import com.morningstar.martgateway.domains.portfolioholding.dao.parsers.HoldingBufferSize;
import com.morningstar.martgateway.domains.portfolioholding.entity.HoldingsView;
import com.morningstar.martgateway.domains.portfolioholding.entity.PortfolioDateType;
import com.morningstar.martgateway.domains.portfolioholding.entity.PortfolioDepth;
import com.morningstar.martgateway.domains.portfolioholding.entity.SuppressionTypeEnum;
import com.morningstar.martgateway.interfaces.model.holdingdata.HoldingDataPoint;
import com.morningstar.martgateway.interfaces.model.holdingdata.HoldingDataRequest;
import com.morningstar.martgateway.interfaces.model.holdingdata.PortfolioDate;
import com.morningstar.martgateway.interfaces.model.holdingdata.PortfolioSetting;
import com.morningstar.martgateway.interfaces.model.holdingdata.SuppressionType;
import com.morningstar.martgateway.interfaces.model.investmentapi.Investment;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class HoldingDataRequestBuilder {

    private final HoldingDataRequest holdingDataRequest;

    public HoldingDataRequest basic() {
        return new HoldingDataRequestBuilder()
                .useCase("view")
                .portfolioDepth(PortfolioDepth.BASE_LEVEL)
                .holdingsView(new HoldingsView("bestAvailable"))
                .portfolioDateType(PortfolioDateType.MOST_RECENT)
                .frequency("m")
                .suppressionTypeEnum(SuppressionTypeEnum.DEFAULT)
                .investments(Set.of(new Investment("investment")))
                .holdingDataPoints(List.of(new HoldingDataPoint("id1", "alias1")))
                .idType(InvestmentApiIdType.SEC_ID)
                .fixedIncomeEnrichmentType(FixedIncomeEnrichmentType.POINT_IN_TIME)
                .build();
    }

    public HoldingDataRequestBuilder() {
        holdingDataRequest = new HoldingDataRequest();
        PortfolioSetting portfolioSetting = new PortfolioSetting();
        PortfolioDate portfolioDate = new PortfolioDate();
        SuppressionType suppressionType = new SuppressionType();
        portfolioSetting.setPortfolioDate(portfolioDate);
        portfolioSetting.setSuppressionType(suppressionType);
        portfolioSetting.setHoldingsDataPoints(Lists.newArrayList());
        holdingDataRequest.setPortfolioSetting(portfolioSetting);
        holdingDataRequest.setInvestments(Sets.newHashSet());
        holdingDataRequest.setBlBatchSize(HoldingBufferSize.getDefaultBlHoldingBufferSize());
        holdingDataRequest.setLtBatchSize(HoldingBufferSize.getDefaultLtHoldingBufferSize());
    }

    public HoldingDataRequestBuilder userId(String userId) {
        holdingDataRequest.setUserId(userId);
        return this;
    }

    public HoldingDataRequestBuilder idType(InvestmentApiIdType idType) {
        holdingDataRequest.setIdType(idType);
        return this;
    }

    public HoldingDataRequestBuilder useCase(String useCase) {
        holdingDataRequest.setUseCase(useCase);
        return this;
    }

    public HoldingDataRequestBuilder investments(Set<Investment> investments) {
        holdingDataRequest.setInvestments(investments);
        return this;
    }

    public HoldingDataRequestBuilder investmentValues(Set<String> investmentValues) {
        holdingDataRequest.setInvestments(investmentValues
                .stream()
                .map(Investment::new)
                .collect(Collectors.toSet())
        );
        return this;
    }

    public HoldingDataRequestBuilder portfolioSetting(PortfolioSetting portfolioSetting) {
        holdingDataRequest.setPortfolioSetting(portfolioSetting);
        return this;
    }

    public HoldingDataRequestBuilder portfolioDepth(PortfolioDepth portfolioDepth) {
        holdingDataRequest.getPortfolioSetting().setPortfolioDepth(portfolioDepth);
        return this;
    }

    public HoldingDataRequestBuilder holdingsView(HoldingsView holdingsView) {
        holdingDataRequest.getPortfolioSetting().setHoldingsView(holdingsView);
        return this;
    }

    public HoldingDataRequestBuilder portfolioDate(PortfolioDate portfolioDate) {
        holdingDataRequest.getPortfolioSetting().setPortfolioDate(portfolioDate);
        return this;
    }

    public HoldingDataRequestBuilder portfolioDateType(PortfolioDateType portfolioDateType) {
        holdingDataRequest.getPortfolioSetting().getPortfolioDate().setType(portfolioDateType);
        return this;
    }

    public HoldingDataRequestBuilder startDate(String startDate) {
        holdingDataRequest.getPortfolioSetting().getPortfolioDate().setStartDate(startDate);
        return this;
    }

    public HoldingDataRequestBuilder endDate(String endDate) {
        holdingDataRequest.getPortfolioSetting().getPortfolioDate().setEndDate(endDate);
        return this;
    }

    public HoldingDataRequestBuilder frequency(String frequency) {
        holdingDataRequest.getPortfolioSetting().getPortfolioDate().setFrequency(frequency);
        return this;
    }

    public HoldingDataRequestBuilder staticDates(Set<String> staticDates) {
        holdingDataRequest.getPortfolioSetting().getPortfolioDate().setStaticDates(staticDates);
        return this;
    }

    public HoldingDataRequestBuilder suppressionType(SuppressionType suppressionType) {
        holdingDataRequest.getPortfolioSetting().setSuppressionType(suppressionType);
        return this;
    }

    public HoldingDataRequestBuilder suppressionClientId(String clientId) {
        holdingDataRequest.getPortfolioSetting().getSuppressionType().setClientId(clientId);
        return this;
    }

    public HoldingDataRequestBuilder suppressionTypeEnum(SuppressionTypeEnum suppressionTypeEnum) {
        holdingDataRequest.getPortfolioSetting().getSuppressionType().setType(suppressionTypeEnum);
        return this;
    }

    public HoldingDataRequestBuilder idMappers(List<IdMapper> idMappers) {
        holdingDataRequest.setIdMappers(idMappers);
        return this;
    }

    public HoldingDataRequestBuilder holdingDataPoints(List<HoldingDataPoint> holdingDataPoints) {
        holdingDataRequest.getPortfolioSetting().getHoldingsDataPoints().addAll(holdingDataPoints);
        return this;
    }

    public HoldingDataRequestBuilder masterPortfolioMap(Map<String, Set<String>> masterPortfolioMap) {
        holdingDataRequest.setMasterPortfolioMap(masterPortfolioMap);
        return this;
    }

    public HoldingDataRequestBuilder cachedEntitlement(CachedEntitlement cachedEntitlement) {
        holdingDataRequest.setCachedEntitlement(cachedEntitlement);
        return this;
    }

    public HoldingDataRequestBuilder isAsync(boolean isAsync) {
        holdingDataRequest.setAsync(isAsync);
        return this;
    }

    public HoldingDataRequestBuilder blBatchSize(int blBatchSize) {
        holdingDataRequest.setBlBatchSize(blBatchSize);
        return this;
    }

    public HoldingDataRequestBuilder ltBatchSize(int ltBatchSize) {
        holdingDataRequest.setLtBatchSize(ltBatchSize);
        return this;
    }

    public HoldingDataRequestBuilder fixedIncomeEnrichmentType(FixedIncomeEnrichmentType fixedIncomeEnrichmentType) {
        holdingDataRequest.getPortfolioSetting().setFixedIncomeEnrichmentType(fixedIncomeEnrichmentType);
        return this;
    }

    public HoldingDataRequest build() {
        return holdingDataRequest;
    }

}
