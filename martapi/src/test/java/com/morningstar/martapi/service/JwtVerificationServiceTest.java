package com.morningstar.martapi.service;

import com.auth0.jwt.interfaces.DecodedJWT;
import com.auth0.jwk.Jwk;
import com.auth0.jwk.JwkProvider;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import java.security.interfaces.RSAPublicKey;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class JwtVerificationServiceTest {
    private JwtVerificationService jwtVerificationService;
    private JwkProvider jwkProvider;
    private DecodedJWT decodedJWT;
    private Jwk jwk;
    private RSAPublicKey publicKey;

    @Before
    public void setUp() throws Exception {
        jwkProvider = mock(JwkProvider.class);
        decodedJWT = mock(DecodedJWT.class);
        jwk = mock(Jwk.class);
        publicKey = mock(RSAPublicKey.class);
        jwtVerificationService = new JwtVerificationService("https://test.jwks.url");
        java.lang.reflect.Field field = JwtVerificationService.class.getDeclaredField("jwkProvider");
        field.setAccessible(true);
        field.set(jwtVerificationService, jwkProvider);
    }

    @Test
    public void testVerifyJwtTokenValid() throws Exception {
        when(decodedJWT.getKeyId()).thenReturn("kid");
        when(jwkProvider.get("kid")).thenReturn(jwk);
        boolean result = jwtVerificationService.verifyJwtToken(decodedJWT);
        assertFalse(result);
    }

    @Test
    public void testVerifyJwtTokenInvalid() throws Exception {
        when(decodedJWT.getKeyId()).thenReturn("kid");
        when(jwkProvider.get("kid")).thenThrow(new RuntimeException("JWK not found"));
        boolean result = jwtVerificationService.verifyJwtToken(decodedJWT);
        assertFalse(result);
    }
}
