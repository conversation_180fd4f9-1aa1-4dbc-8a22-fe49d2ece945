package com.morningstar.martapi.service;

import com.auth0.jwt.interfaces.DecodedJWT;
import com.auth0.jwk.Jwk;
import com.auth0.jwk.JwkProvider;
import com.auth0.jwt.interfaces.JWTVerifier;
import com.github.benmanes.caffeine.cache.Cache;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import java.security.interfaces.RSAPublicKey;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class JwtVerificationServiceTest {
    private JwtVerificationService jwtVerificationService;
    private JwkProvider jwkProvider;
    private DecodedJWT decodedJWT;
    private Jwk jwk;
    private RSAPublicKey publicKey;
    private Cache<String, JWTVerifier> verifierCache;

    @Before
    public void setUp() throws Exception {
        jwkProvider = mock(JwkProvider.class);
        decodedJWT = mock(DecodedJWT.class);
        jwk = mock(Jwk.class);
        publicKey = mock(RSAPublicKey.class);
        jwtVerificationService = new JwtVerificationService("https://test.jwks.url");

        // Set up reflection to access private fields
        java.lang.reflect.Field jwkProviderField = JwtVerificationService.class.getDeclaredField("jwkProvider");
        jwkProviderField.setAccessible(true);
        jwkProviderField.set(jwtVerificationService, jwkProvider);

        java.lang.reflect.Field cacheField = JwtVerificationService.class.getDeclaredField("verifierCache");
        cacheField.setAccessible(true);
        verifierCache = (Cache<String, JWTVerifier>) cacheField.get(jwtVerificationService);
    }

    @Test
    public void testVerifyJwtTokenWithNullKeyId() throws Exception {
        when(decodedJWT.getKeyId()).thenReturn(null);
        boolean result = jwtVerificationService.verifyJwtToken(decodedJWT);
        assertFalse(result);
        verify(jwkProvider, never()).get(anyString());
    }

    @Test
    public void testVerifyJwtTokenInvalid() throws Exception {
        when(decodedJWT.getKeyId()).thenReturn("kid");
        when(jwkProvider.get("kid")).thenThrow(new RuntimeException("JWK not found"));
        boolean result = jwtVerificationService.verifyJwtToken(decodedJWT);
        assertFalse(result);
    }

    @Test
    public void testCacheHitOnSecondCall() throws Exception {
        String keyId = "test-key-id";
        when(decodedJWT.getKeyId()).thenReturn(keyId);
        when(jwkProvider.get(keyId)).thenReturn(jwk);
        when(jwk.getPublicKey()).thenReturn(publicKey);

        // First call should create and cache the verifier
        assertEquals(0, jwtVerificationService.getCacheSize());
        jwtVerificationService.verifyJwtToken(decodedJWT);
        assertEquals(1, jwtVerificationService.getCacheSize());

        // Second call should use cached verifier
        jwtVerificationService.verifyJwtToken(decodedJWT);

        // JwkProvider should only be called once (for the first call)
        verify(jwkProvider, times(1)).get(keyId);
        assertEquals(1, jwtVerificationService.getCacheSize());
    }

    @Test
    public void testClearCache() throws Exception {
        String keyId = "test-key-id";
        when(decodedJWT.getKeyId()).thenReturn(keyId);
        when(jwkProvider.get(keyId)).thenReturn(jwk);
        when(jwk.getPublicKey()).thenReturn(publicKey);

        // Add something to cache
        jwtVerificationService.verifyJwtToken(decodedJWT);
        assertEquals(1, jwtVerificationService.getCacheSize());

        // Clear cache
        jwtVerificationService.clearVerifierCache();
        assertEquals(0, jwtVerificationService.getCacheSize());
    }

    @Test
    public void testGetCacheStats() throws Exception {
        String stats = jwtVerificationService.getCacheStats();
        assertNotNull(stats);
        assertTrue(stats.contains("Cache stats"));
        assertTrue(stats.contains("Size:"));
        assertTrue(stats.contains("Hit rate:"));
        assertTrue(stats.contains("Evictions:"));
    }

    @Test
    public void testMultipleKeyIdsCached() throws Exception {
        String keyId1 = "key-1";
        String keyId2 = "key-2";
        DecodedJWT jwt1 = mock(DecodedJWT.class);
        DecodedJWT jwt2 = mock(DecodedJWT.class);

        when(jwt1.getKeyId()).thenReturn(keyId1);
        when(jwt2.getKeyId()).thenReturn(keyId2);
        when(jwkProvider.get(keyId1)).thenReturn(jwk);
        when(jwkProvider.get(keyId2)).thenReturn(jwk);
        when(jwk.getPublicKey()).thenReturn(publicKey);

        // Verify both tokens
        jwtVerificationService.verifyJwtToken(jwt1);
        jwtVerificationService.verifyJwtToken(jwt2);

        // Should have 2 cached verifiers
        assertEquals(2, jwtVerificationService.getCacheSize());

        // Verify each key was fetched once
        verify(jwkProvider, times(1)).get(keyId1);
        verify(jwkProvider, times(1)).get(keyId2);
    }
}
