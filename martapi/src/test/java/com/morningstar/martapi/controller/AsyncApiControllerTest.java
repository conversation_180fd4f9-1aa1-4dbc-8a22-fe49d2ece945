package com.morningstar.martapi.controller;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.morningstar.martapi.entity.AsyncApiResponseEntity;
import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.service.AsyncApiService;
import com.morningstar.martapi.service.JwtVerificationService;
import com.morningstar.martapi.validator.AuthTokenValidator;
import com.morningstar.martapi.validator.ProductIdValidator;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.investmentapi.ColumnLimitValidator;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.martcommon.entity.payload.gridview.GridviewDataPoint;
import com.morningstar.martgateway.domains.entitlement.service.DataEntitlementService;
import com.morningstar.martgateway.infrastructures.config.ProductIdsRegistry;
import com.morningstar.martgateway.interfaces.model.investmentapi.Investment;
import com.morningstar.martgateway.interfaces.model.investmentapi.InvestmentApiRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;

import com.morningstar.martgateway.util.EquityDatapointUtil;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.UUID;

import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class AsyncApiControllerTest {

    private static final String MOCK_TOKEN = "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6Ik1FWXlOakZDTVRVME0wRkdSRGxCUTBVeE56RTFRamt6TWtaR1JUTTJOME01TlVZelJFWTJOdyJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ULSU3VZx76Xxk9tG6qxUlm2x_ABohvUjsK-STjEINb7Ju_RsOMAmCbNL5r2DYlHFjVbSaf0sikNpQCNQuT2x5k3psA4fhxyqy7nP346fogDVFinyINzVBhw2OfO9wzLuuWLWDSM94tV_oStfcfaaYonwpSO_0kD8ey9wOqlfPYKoJbheXQa014qldmIv-EBVWUj0Zd0NsWu4JbWt9SD-dh9lpZMcErM5i58YNgXkKA_O0MvbsVJrtbQpTpiPXbCTuhhY6WTWRUcT7NOSnLGuHSsE5fj6bmZb9ei2DfSmqJRS9Ft2-gDdlmJ-1ZaHEcEjtzEtuEohYmRtQ7Vhljju3w";

    private AsyncApiController asyncApiController;

    @Mock
    ProductIdsRegistry productIdsRegistry;
    @Mock
    AsyncApiService asyncApiService;
    @Mock
    DataEntitlementService<InvestmentApiRequest> dataEntitlementService;

    @BeforeEach
    public void setup(){
        asyncApiController = new AsyncApiController(
                asyncApiService,
                new RequestValidationHandler<>(Collections.emptyList(), Collections.emptyList(), dataEntitlementService),
                Schedulers.fromExecutor(Executors.newSingleThreadExecutor())
        );
    }

    @Test
    public void testFetchAsyncData() {
        AsyncApiResponseEntity mockResponse = AsyncApiResponseEntity.builder().jobId("job1").jobStatus("Submitted").build();
        when(asyncApiService.fetchAsyncData(any())).thenReturn(mockResponse);
        InvestmentApiRequest investmentApiRequest = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(Collections.singletonList(Investment.builder().id("F0000USA").build()))
                .dataPoints(Collections.singletonList(GridviewDataPoint.builder().dataPointId("HS0100").build()))
                .build();
        Mono<AsyncApiResponseEntity> response = asyncApiController.fetchAsyncData("product1", UUID.randomUUID().toString(), createToken(), investmentApiRequest);

        StepVerifier.create(response)
                .expectSubscription()
                .assertNext(r -> assertEquals("Submitted", r.getJobStatus()))
                .verifyComplete();
    }

    @Test
    public void testFetchAsyncDataHasError() {
        doThrow(new RuntimeException()).when(asyncApiService).fetchAsyncData(any());
        InvestmentApiRequest investmentApiRequest = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(Collections.singletonList(Investment.builder().id("F0000USA").build()))
                .dataPoints(Collections.singletonList(GridviewDataPoint.builder().dataPointId("HS0100").build()))
                .build();

        StepVerifier.create(asyncApiController.fetchAsyncData("product1", UUID.randomUUID().toString(), createToken(), investmentApiRequest))
                .expectErrorMatches(e -> e instanceof InvestmentApiValidationException && ((InvestmentApiValidationException) e).getStatus().equals(Status.INTERNAL_ERROR))
                .verify();
    }

    @Test
    public void testGetAsyncStatusHasError() {
        doThrow(new RuntimeException()).when(asyncApiService).getGridviewStatus(any(),any(),any(),any());

        StepVerifier.create(asyncApiController.getAsyncJobStatus("product1", "request1", "","token", "job"))
                .expectErrorMatches(e -> e instanceof InvestmentApiValidationException && ((InvestmentApiValidationException) e).getStatus().equals(Status.INTERNAL_ERROR))
                .verify();
    }

    @Test
    public void testGetAsyncStatus() {
        AsyncApiResponseEntity mockResponse = AsyncApiResponseEntity.builder().jobStatus("Submitted").build();
        when(asyncApiService.getGridviewStatus(any(),any(),any(),any())).thenReturn(mockResponse);
        Mono<AsyncApiResponseEntity> response = asyncApiController.getAsyncJobStatus("product1", "request1", "userId","token", "job");

        StepVerifier.create(response)
                .expectSubscription()
                .assertNext(r -> Assert.assertEquals("Submitted", r.getJobStatus()))
                .verifyComplete();
    }

    private String createToken() {
        return JWT.create()
                .withExpiresAt(ZonedDateTime.now(ZoneOffset.UTC).plusDays(1).toInstant())
                .withClaim("https://morningstar.com/mstar_id", UUID.randomUUID().toString()).sign(Algorithm.none());
    }

    @Test
    public void testProductIdValidator() {
        asyncApiController = new AsyncApiController(
                asyncApiService,
                new RequestValidationHandler<>(List.of(new ProductIdValidator(productIdsRegistry)), Collections.emptyList(), dataEntitlementService),
                Schedulers.fromExecutor(Executors.newSingleThreadExecutor()));

        InvestmentApiValidationException exception = assertThrows(InvestmentApiValidationException.class, () ->
                asyncApiController.fetchAsyncData("",  "request1", "token", InvestmentApiRequest.builder().build())
        );
        assertEquals(Status.INVALID_PRODUCT_ID.getMessage(), exception.getStatus().getMessage());

        exception = assertThrows(InvestmentApiValidationException.class, () ->
                asyncApiController.fetchAsyncData("", "request1", "token", InvestmentApiRequest.builder().build())
        );
        assertEquals(Status.INVALID_PRODUCT_ID, exception.getStatus());

        when(productIdsRegistry.isEmpty()).thenReturn(false);
        when(productIdsRegistry.hasProductId(anyString())).thenReturn(true);
        AsyncApiResponseEntity mockResponse = AsyncApiResponseEntity.builder().jobId("job1").jobStatus("Submitted").build();
        when(asyncApiService.fetchAsyncData(any())).thenReturn(mockResponse);

        Mono<AsyncApiResponseEntity> result = asyncApiController.fetchAsyncData("product1", "request1", "token", InvestmentApiRequest.builder().build());

        StepVerifier.create(result).assertNext(Assertions::assertNotNull).verifyComplete();
    }

    @Test
    public void testAuthTokenValidator() {
        JwtVerificationService jwtVerificationService = mock(JwtVerificationService.class);
        asyncApiController = new AsyncApiController(
                asyncApiService,
                new RequestValidationHandler<>(List.of(new AuthTokenValidator(jwtVerificationService)), Collections.emptyList()),
                Schedulers.fromExecutor(Executors.newSingleThreadExecutor()));

        InvestmentApiValidationException exception = assertThrows(InvestmentApiValidationException.class, () ->
                asyncApiController.fetchAsyncData("",  "request1", "token", InvestmentApiRequest.builder().build())
        );
        assertEquals(Status.INVALID_TOKEN.getMessage(), exception.getStatus().getMessage());
        exception = assertThrows(InvestmentApiValidationException.class, () ->
                asyncApiController.fetchAsyncData("product1", "request1", MOCK_TOKEN, InvestmentApiRequest.builder().build()));
        assertEquals(Status.EXPIRED_TOKEN, exception.getStatus());
    }

    @Test
    public void testFetchAsyncDataHasValidationError() {
        doThrow(new InvestmentApiValidationException(Status.BAD_REQUEST)).when(asyncApiService).fetchAsyncData(any());
        InvestmentApiRequest investmentApiRequest = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(Collections.singletonList(Investment.builder().id("F0000USA").build()))
                .dataPoints(Collections.singletonList(GridviewDataPoint.builder().dataPointId("HS0100").build()))
                .build();

        StepVerifier.create(asyncApiController.fetchAsyncData("product1", UUID.randomUUID().toString(), createToken(), investmentApiRequest))
                .expectErrorMatches(e -> e instanceof InvestmentApiValidationException && ((InvestmentApiValidationException) e).getStatus().equals(Status.BAD_REQUEST))
                .verify();
    }

    @Test
    public void testGetAsyncStatusHasValidationError() {
        doThrow(new InvestmentApiValidationException(Status.BAD_REQUEST)).when(asyncApiService).getGridviewStatus(any(),any(),any(),any());

        StepVerifier.create(asyncApiController.getAsyncJobStatus("product1", "request1", "userId","token", "job"))
                .expectErrorMatches(e -> e instanceof InvestmentApiValidationException && ((InvestmentApiValidationException) e).getStatus().equals(Status.BAD_REQUEST))
                .verify();
    }

    @Test
    public void testFailOnManyColumnsRequested() {
        asyncApiController = new AsyncApiController(
                asyncApiService,
                new RequestValidationHandler<>(Collections.emptyList(), List.of(new ColumnLimitValidator(new EquityDatapointUtil()))),
                Schedulers.fromExecutor(Executors.newSingleThreadExecutor()));
        List<GridviewDataPoint> dataPoints = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            dataPoints.add(GridviewDataPoint.builder()
                    .dataPointId("HP026" + i)
                    .alias("HP026a" + i)
                    .startDate("1990-01-01")
                    .endDate("2024-01-01")
                    .frequency("d")
                    .windowType("4")
                    .windowSize("0")
                    .stepSize("1")
                    .build());
        }
        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase("view")
                .dataPoints(dataPoints)
                .build();
        InvestmentApiValidationException exception = assertThrows(InvestmentApiValidationException.class, () ->
                asyncApiController.fetchAsyncData("product1", UUID.randomUUID().toString(), createToken(), request
        ));
        assertEquals(Status.BAD_REQUEST.getCode(), exception.getStatus().getCode());
        assertTrue(exception.getMessage().startsWith("Request input exceeds maximum column limit of [89421] columns"));
    }
}
