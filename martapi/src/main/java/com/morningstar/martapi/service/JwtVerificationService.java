package com.morningstar.martapi.service;

import com.auth0.jwk.Jwk;
import com.auth0.jwk.JwkException;
import com.auth0.jwk.JwkProvider;
import com.auth0.jwk.JwkProviderBuilder;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.auth0.jwt.interfaces.JWTVerifier;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.morningstar.martgateway.infrastructures.log.LogAttribute;
import com.morningstar.martgateway.infrastructures.log.LogEntity;
import com.morningstar.martgateway.infrastructures.log.LogEntry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import java.security.interfaces.RSAPublicKey;
import java.util.concurrent.TimeUnit;

@Slf4j
public class JwtVerificationService {

    private final JwkProvider jwkProvider;
    private final Cache<String, JWTVerifier> verifierCache;

    public JwtVerificationService(@Value("${jwt.jwks.url}") String jwksUrl) {
        this.jwkProvider = new JwkProviderBuilder(jwksUrl)
                .cached(10, 24, TimeUnit.HOURS)
                .build();

        this.verifierCache = Caffeine.newBuilder()
                .expireAfterWrite(12, TimeUnit.HOURS)
                .maximumSize(100)
                .build();
    }

    public boolean verifyJwtToken(DecodedJWT jwt) {
        try {
            String keyId = jwt.getKeyId();
            if (keyId == null) {
                LogEntry.error(
                        new LogEntity(LogAttribute.EVENT_TYPE, "JWT_VERIFICATION_ERROR"),
                        new LogEntity(LogAttribute.EXCEPTION_MESSAGE, "JWT key ID is null")
                );
                return false;
            }

            JWTVerifier verifier = verifierCache.getIfPresent(keyId);

            if (verifier == null) {
                log.debug("JWT verifier cache miss for key ID: {}", keyId);
                Jwk jwk = jwkProvider.get(keyId);
                Algorithm algorithm = Algorithm.RSA256((RSAPublicKey) jwk.getPublicKey(), null);
                verifier = JWT.require(algorithm).build();
                verifierCache.put(keyId, verifier);
                log.debug("JWT verifier cached for key ID: {}", keyId);
            } else {
                log.debug("JWT verifier cache hit for key ID: {}", keyId);
            }

            verifier.verify(jwt);
            return true;
        } catch (Exception e) {
            LogEntry.error(
                    new LogEntity(LogAttribute.EVENT_TYPE, "JWT_VERIFICATION_ERROR"),
                    new LogEntity(LogAttribute.EXCEPTION_TYPE, e.getClass()),
                    new LogEntity(LogAttribute.EXCEPTION_MESSAGE, e.getMessage())
            );
            return false;
        }
    }


}