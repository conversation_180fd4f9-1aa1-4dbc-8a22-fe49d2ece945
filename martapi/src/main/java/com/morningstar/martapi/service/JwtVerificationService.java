package com.morningstar.martapi.service;

import com.auth0.jwk.Jwk;
import com.auth0.jwk.JwkException;
import com.auth0.jwk.JwkProvider;
import com.auth0.jwk.JwkProviderBuilder;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.auth0.jwt.interfaces.JWTVerifier;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.morningstar.martgateway.infrastructures.log.LogAttribute;
import com.morningstar.martgateway.infrastructures.log.LogEntity;
import com.morningstar.martgateway.infrastructures.log.LogEntry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import java.security.interfaces.RSAPublicKey;
import java.util.concurrent.TimeUnit;

@Slf4j
public class JwtVerificationService {

    private final JwkProvider jwkProvider;
    private final Cache<String, JWTVerifier> verifierCache;

    public JwtVerificationService(@Value("${jwt.jwks.url}") String jwksUrl) {
        this.jwkProvider = new JwkProviderBuilder(jwksUrl)
                .cached(10, 24, TimeUnit.HOURS)
                .build();

        // Initialize verifier cache with 12-hour expiration
        this.verifierCache = Caffeine.newBuilder()
                .expireAfterWrite(12, TimeUnit.HOURS)
                .maximumSize(100) // Reasonable limit for number of cached verifiers
                .recordStats() // Enable statistics recording
                .build();
    }

    public boolean verifyJwtToken(DecodedJWT jwt) {
        try {
            String keyId = jwt.getKeyId();
            if (keyId == null) {
                LogEntry.error(
                        new LogEntity(LogAttribute.EVENT_TYPE, "JWT_VERIFICATION_ERROR"),
                        new LogEntity(LogAttribute.EXCEPTION_MESSAGE, "JWT key ID is null")
                );
                return false;
            }

            // Try to get verifier from cache first
            JWTVerifier verifier = verifierCache.getIfPresent(keyId);

            if (verifier == null) {
                // Cache miss - create new verifier
                log.debug("JWT verifier cache miss for key ID: {}", keyId);
                Jwk jwk = jwkProvider.get(keyId);
                Algorithm algorithm = Algorithm.RSA256((RSAPublicKey) jwk.getPublicKey(), null);
                verifier = JWT.require(algorithm).build();

                // Cache the verifier
                verifierCache.put(keyId, verifier);
                log.debug("JWT verifier cached for key ID: {}", keyId);
            } else {
                log.debug("JWT verifier cache hit for key ID: {}", keyId);
            }

            verifier.verify(jwt);
            return true;
        } catch (Exception e) {
            LogEntry.error(
                    new LogEntity(LogAttribute.EVENT_TYPE, "JWT_VERIFICATION_ERROR"),
                    new LogEntity(LogAttribute.EXCEPTION_TYPE, e.getClass()),
                    new LogEntity(LogAttribute.EXCEPTION_MESSAGE, e.getMessage())
            );
            return false;
        }
    }

    /**
     * Clears the JWT verifier cache. Useful for cache invalidation scenarios.
     */
    public void clearVerifierCache() {
        verifierCache.invalidateAll();
        log.info("JWT verifier cache cleared");
    }

    /**
     * Gets the current size of the verifier cache.
     * @return the number of cached verifiers
     */
    public long getCacheSize() {
        return verifierCache.estimatedSize();
    }

    /**
     * Gets cache statistics for monitoring purposes.
     * @return cache stats as a formatted string
     */
    public String getCacheStats() {
        var stats = verifierCache.stats();
        return String.format("Cache stats - Size: %d, Hit rate: %.2f%%, Evictions: %d",
                verifierCache.estimatedSize(),
                stats.hitRate() * 100,
                stats.evictionCount());
    }
}