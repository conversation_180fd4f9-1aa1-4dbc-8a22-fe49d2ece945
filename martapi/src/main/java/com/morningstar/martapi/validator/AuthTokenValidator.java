package com.morningstar.martapi.validator;

import com.auth0.jwt.JWT;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.service.JwtVerificationService;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

public class AuthTokenValidator implements Validator<HeadersAndParams> {

    private final JwtVerificationService jwtVerificationService;
    public AuthTokenValidator(JwtVerificationService jwtVerificationService) {
        this.jwtVerificationService = jwtVerificationService;
    }

    @Override
    public void validate(HeadersAndParams headersAndParams) throws InvestmentApiValidationException {
        String token = headersAndParams.getAuthorizationToken();
        try {
            if (StringUtils.startsWith(token, "Bearer ")) {
                token = token.substring(7);
            }
            DecodedJWT decodedToken = JWT.decode(token);

            if (isExpiredToken(decodedToken)) {
                throw new InvestmentApiValidationException(Status.EXPIRED_TOKEN);
            }
            if(!this.jwtVerificationService.verifyJwtToken(decodedToken)) {
                throw new InvestmentApiValidationException(Status.INVALID_TOKEN);
            }
            if (StringUtils.isEmpty(decodedToken.getClaim("https://morningstar.com/mstar_id").asString()) &&
                    StringUtils.isEmpty(decodedToken.getClaim("https://morningstar.com/config_id").asString())) {
                throw new InvestmentApiValidationException(Status.INVALID_TOKEN);
            }
        } catch (JWTDecodeException e) {
            throw new InvestmentApiValidationException(Status.INVALID_TOKEN);
        }
    }

    private boolean isExpiredToken(DecodedJWT token) {
        Date tokenExpireDate = token.getExpiresAt();
        return tokenExpireDate.before(new Date());
    }
}
