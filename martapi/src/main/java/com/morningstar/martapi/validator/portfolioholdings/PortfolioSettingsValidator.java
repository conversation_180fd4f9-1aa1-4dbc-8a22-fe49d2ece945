package com.morningstar.martapi.validator.portfolioholdings;

import com.morningstar.martapi.exception.HoldingValidationException;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.martgateway.domains.fixedincome.entity.FixedIncomeEnrichmentType;
import com.morningstar.martgateway.domains.portfolioholding.entity.HoldingsView;
import com.morningstar.martgateway.domains.portfolioholding.entity.PortfolioDepth;
import com.morningstar.martgateway.domains.portfolioholding.entity.SuppressionTypeEnum;
import com.morningstar.martgateway.domains.portfolioholding.service.entitlement.SuppressionClientIdUtil;
import com.morningstar.martgateway.interfaces.model.holdingdata.HoldingDataRequest;
import com.morningstar.martgateway.interfaces.model.holdingdata.PortfolioDate;
import com.morningstar.martgateway.interfaces.model.holdingdata.PortfolioSetting;
import com.morningstar.martgateway.interfaces.model.holdingdata.SuppressionType;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;

public class PortfolioSettingsValidator implements Validator<HoldingDataRequest> {

    private static final DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE;

    @Override
    public void validate(HoldingDataRequest request) throws RuntimeException {

        PortfolioSetting portfolioSetting = request.getPortfolioSetting();
        if (portfolioSetting == null) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Mandatory field `portfolioSetting` is missing"));
        }
        validatePortfolioDepth(portfolioSetting.getPortfolioDepth());
        validateHoldingsView(portfolioSetting.getHoldingsView());
        validatePortfolioDate(portfolioSetting.getPortfolioDate());
        validateSuppressionType(portfolioSetting.getSuppressionType());
        validateSuppressionClientId(request);
        setDefaultFixedIncomeEnrichmentTypeIfMissing(portfolioSetting);
    }

    private static void validateSuppressionClientId(HoldingDataRequest request) {
        if (!SuppressionClientIdUtil.isSuppressionClientIdValid(request, request.getCachedEntitlement())) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Suppression client id does not match id from token"));
        }
    }

    private static void setDefaultFixedIncomeEnrichmentTypeIfMissing(PortfolioSetting portfolioSetting) {
        if (portfolioSetting.getFixedIncomeEnrichmentType() == null) {
            portfolioSetting.setFixedIncomeEnrichmentType(FixedIncomeEnrichmentType.POINT_IN_TIME);
        }
    }

    private static void validateSuppressionType(SuppressionType suppressionType) {
        if (suppressionType == null) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Mandatory field `suppressionType` is missing"));
        }
        SuppressionTypeEnum type = suppressionType.getType();
        if (type == null) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Mandatory field `suppressionType.type` is missing"));
        }
        if (type == SuppressionTypeEnum.CLIENT_SPECIFIC) {
            if (StringUtils.isEmpty(suppressionType.getClientId())) {
                throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Mandatory field `clientId` is missing for type `clientSpecific`"));
            }
        }
        if (type == SuppressionTypeEnum.DEFAULT) {
            if (StringUtils.isNotEmpty(suppressionType.getClientId())) {
                throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Prohibited field `clientId` is present for type `default`"));
            }
        }
    }

    private static void validatePortfolioDepth(PortfolioDepth portfolioDepth) {
        if (portfolioDepth == null) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Mandatory field `portfolioDepth` is missing"));
        }
    }

    private static void validateHoldingsView(HoldingsView holdingsView) {
        if (holdingsView == null) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Mandatory field `holdingsView` is missing"));
        }
        if (!holdingsView.isBestAvailable() && !holdingsView.isN() && !holdingsView.isFull()) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Invalid holdingsView value. Valid values: [bestAvailable, full] or an integer >= 1"));
        }
    }

    private static void validatePortfolioDate(PortfolioDate portfolioDate) {
        if (portfolioDate == null) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Mandatory field `portfolioDate` is missing"));
        }
        if (portfolioDate.getType() == null) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Mandatory field `portfolioDate.type` is missing"));
        }

        switch (portfolioDate.getType()) {
            case MOST_RECENT:
                validateMostRecentPortfolioDate(portfolioDate);
                break;
            case TIME_SERIES:
                validateTimeSeriesPortfolioDate(portfolioDate);
                break;
            case STATIC_DATES:
                validateStaticDatesPortfolioDate(portfolioDate);
                break;
        }
    }

    private static boolean isDate(String date) {
        try {
            LocalDate.parse(date, formatter);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    private static void validateMostRecentPortfolioDate(PortfolioDate portfolioDate) {
        String frequency = portfolioDate.getFrequency();
        if (StringUtils.isEmpty(frequency)) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Mandatory field `frequency` is missing"));
        } else if (!frequency.equals("d") && !frequency.equals("m")) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Invalid frequency value. Valid values: [d, m]"));
        }
        if (portfolioDate.getStartDate() != null) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Prohibited field `startDate` is provided for type `mostRecent`"));
        }
        if (portfolioDate.getEndDate() != null) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Prohibited field `endDate` is provided for type `mostRecent`"));
        }
        if (portfolioDate.getStaticDates() != null && !portfolioDate.getStaticDates().isEmpty()) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Prohibited field `dates` is provided for type `mostRecent`"));
        }
    }

    private static void validateTimeSeriesPortfolioDate(PortfolioDate portfolioDate) {
        if (portfolioDate.getStaticDates() != null && !portfolioDate.getStaticDates().isEmpty()) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Prohibited field `dates` is provided for type `timeSeries`"));
        }
        String frequency = portfolioDate.getFrequency();
        if (StringUtils.isEmpty(frequency)) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Mandatory field `frequency` is missing"));
        } else if (!frequency.equals("d") && !frequency.equals("m")) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Invalid frequency value. Valid values: [d, m]"));
        }
        String startDate = portfolioDate.getStartDate();
        if (StringUtils.isEmpty(startDate)) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Mandatory field `startDate` is missing"));
        }
        if (!isDate(startDate)) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Invalid `startDate`. Valid format is [yyyy-MM-dd]"));
        }
        String endDate = portfolioDate.getEndDate();
        if (StringUtils.isEmpty(endDate)) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Mandatory field `endDate` is missing"));
        }
        if (!isDate(endDate)) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Invalid `endDate`. Valid format is [yyyy-MM-dd]"));
        }
        if (startDate.compareTo(endDate) > 0) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("startDate cannot be after endDate"));
        }
        String dateNow = formatter.format(LocalDate.now());
        if (endDate.compareTo(dateNow) > 0) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("endDate cannot be after today"));
        }
    }

    private static void validateStaticDatesPortfolioDate(PortfolioDate portfolioDate) {
        Set<String> staticDates = portfolioDate.getStaticDates();
        if (portfolioDate.getStartDate() != null) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Prohibited field `startDate` is provided for type `staticDates`"));
        }
        if (portfolioDate.getEndDate() != null) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Prohibited field `endDate` is provided for type `staticDates`"));
        }
        if (staticDates == null || staticDates.isEmpty()) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Mandatory field `dates` is missing or empty for type `staticDates`"));
        }
        if (staticDates.stream().anyMatch(date -> !isDate(date))) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Invalid date format in `dates`. Valid format is [yyyy-MM-dd]"));
        }
    }
}
